#!/usr/bin/env python3
"""
Demo script showcasing the Career Coach Agents system.
"""

import asyncio
import sys
import os

# Add src to path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from career_coaches.domain.coach_factory import CoachFactory


def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)


def print_coach_info(coach):
    """Print detailed coach information."""
    print(f"\n👨‍💼 {coach.name}")
    print(f"📋 Specialty: {coach.specialty}")
    print(f"\n🎯 Focus Areas:")
    for area in coach.focus_areas:
        print(f"   • {area}")
    print(f"\n💡 Approach:")
    print(f"   {coach.approach[:200]}...")


def demo_coach_profiles():
    """Demonstrate all available career coaches."""
    print_header("Career Coach Profiles")
    
    coach_factory = CoachFactory()
    available_coaches = coach_factory.get_available_coaches()
    
    print(f"\n🌟 Available Coaches: {len(available_coaches)}")
    
    for coach_id in available_coaches:
        coach = coach_factory.get_coach(coach_id)
        print_coach_info(coach)
        print("\n" + "-" * 40)


def demo_multi_user_scenarios():
    """Demonstrate multi-user conversation scenarios."""
    print_header("Multi-User Conversation Scenarios")
    
    scenarios = [
        {
            "user_id": "alice_001",
            "coach_id": "career_assessment",
            "scenario": "Recent graduate seeking career direction",
            "sample_query": "I just graduated with a computer science degree but I'm not sure if I want to be a software developer. What are my options?"
        },
        {
            "user_id": "bob_002", 
            "coach_id": "resume_builder",
            "scenario": "Mid-career professional updating resume",
            "sample_query": "I've been a project manager for 5 years and want to transition to product management. How should I update my resume?"
        },
        {
            "user_id": "carol_003",
            "coach_id": "linkedin_optimizer", 
            "scenario": "Entrepreneur building personal brand",
            "sample_query": "I'm starting my own consulting business and need to build my LinkedIn presence. Where do I start?"
        },
        {
            "user_id": "david_004",
            "coach_id": "networking_strategy",
            "scenario": "Introvert seeking networking guidance",
            "sample_query": "I'm an introvert but I know I need to network for my career. What strategies work for people like me?"
        }
    ]
    
    coach_factory = CoachFactory()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Scenario {i}: {scenario['scenario']}")
        print(f"👤 User ID: {scenario['user_id']}")
        
        coach = coach_factory.get_coach(scenario['coach_id'])
        print(f"🤝 Coach: {coach.name} ({coach.specialty})")
        
        thread_id = f"{scenario['user_id']}_{scenario['coach_id']}"
        print(f"🔗 Thread ID: {thread_id}")
        
        print(f"💬 Sample Query:")
        print(f"   \"{scenario['sample_query']}\"")
        
        print(f"\n🎯 Expected Coaching Focus:")
        focus_areas = coach.focus_areas[:3]  # Show first 3 focus areas
        for area in focus_areas:
            print(f"   • {area}")
        
        print("\n" + "-" * 50)


def demo_api_structure():
    """Demonstrate API structure and endpoints."""
    print_header("API Structure & Endpoints")
    
    print("\n🌐 Combined API Platform (Port 8000)")
    print("   GET  /                     - Platform information")
    print("   GET  /health               - Health check")
    print("   GET  /services             - Available services")
    
    print("\n🎯 Career Coaches API (/career-coaches)")
    print("   POST /career-coaches/chat                - Single message")
    print("   WS   /career-coaches/ws/chat             - Streaming chat")
    print("   GET  /career-coaches/coaches             - List coaches")
    print("   POST /career-coaches/reset-memory        - Reset memory")
    
    print("\n📚 PhiloAgents API (/philoagents)")
    print("   POST /philoagents/chat                   - Philosopher chat")
    print("   WS   /philoagents/ws/chat                - Streaming chat")
    print("   POST /philoagents/reset-memory           - Reset memory")
    
    print("\n💡 Example API Call:")
    print("""
    curl -X POST "http://localhost:8000/career-coaches/chat" \\
         -H "Content-Type: application/json" \\
         -d '{
           "message": "I need help with my career direction",
           "coach_id": "career_assessment",
           "user_id": "demo_user_001",
           "user_context": "Recent graduate feeling uncertain",
           "session_goals": ["identify_strengths", "explore_options"]
         }'
    """)


def demo_cli_tools():
    """Demonstrate available CLI tools."""
    print_header("CLI Tools & Commands")
    
    print("\n🔧 Coach Management:")
    print("   python tools/list_career_coaches.py")
    print("   python tools/call_career_coach.py --coach-id career_assessment --user-id user_001 --query 'Hello'")
    
    print("\n🧠 Memory Management:")
    print("   python tools/reset_career_coach_memory.py --user-id user_001")
    print("   python tools/create_career_coach_memory.py --use-sample-data")
    
    print("\n📊 Evaluation & Testing:")
    print("   python tools/generate_career_coach_evaluation_dataset.py")
    print("   python tools/evaluate_career_coach.py --dataset-name career_coach_evaluation")
    
    print("\n🧪 Testing:")
    print("   python test_career_coaches.py")
    
    print("\n🚀 Server Startup:")
    print("   python src/main.py                                    # Combined API")
    print("   python src/career_coaches/infrastructure/api.py       # Career Coaches only")


def demo_architecture_benefits():
    """Demonstrate architecture benefits."""
    print_header("Architecture Benefits")
    
    benefits = [
        {
            "feature": "Multi-User Support",
            "description": "Each user maintains isolated conversation history and state",
            "example": "user_001_career_assessment vs user_002_career_assessment threads"
        },
        {
            "feature": "Modular Design", 
            "description": "Shared components between PhiloAgents and Career Coaches",
            "example": "Common MongoDB client, prompt management, RAG components"
        },
        {
            "feature": "Specialized Expertise",
            "description": "Each coach has unique knowledge and approach",
            "example": "Resume builder focuses on ATS optimization, LinkedIn optimizer on personal branding"
        },
        {
            "feature": "Scalable Memory",
            "description": "Short-term conversation memory with long-term knowledge base",
            "example": "Automatic summarization after 30 messages, persistent career knowledge"
        },
        {
            "feature": "Comprehensive Evaluation",
            "description": "Built-in metrics and monitoring for coaching effectiveness",
            "example": "Response relevance, actionability, empathy scoring"
        },
        {
            "feature": "Streaming Responses",
            "description": "Real-time conversation experience via WebSocket",
            "example": "Immediate feedback during coaching sessions"
        }
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"\n{i}. 🎯 {benefit['feature']}")
        print(f"   📝 {benefit['description']}")
        print(f"   💡 Example: {benefit['example']}")


def main():
    """Run the complete demo."""
    print("🚀 Career Coach Agents - System Demo")
    print("Built on PhiloAgents Architecture with Multi-User Support")
    
    try:
        demo_coach_profiles()
        demo_multi_user_scenarios()
        demo_api_structure()
        demo_cli_tools()
        demo_architecture_benefits()
        
        print_header("Next Steps")
        print("\n1. 🔧 Set up your environment:")
        print("   - Configure .env file with API keys")
        print("   - Start MongoDB instance")
        print("   - Install dependencies")
        
        print("\n2. 🧪 Test the system:")
        print("   python test_career_coaches.py")
        
        print("\n3. 🚀 Start the API:")
        print("   python src/main.py")
        
        print("\n4. 💬 Try a conversation:")
        print("   python tools/call_career_coach.py --coach-id career_assessment --user-id your_user_id --query 'Hello'")
        
        print("\n🎉 Happy Coaching!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("Make sure you're in the philoagents-api directory and have the src/ folder set up correctly.")


if __name__ == "__main__":
    main()
