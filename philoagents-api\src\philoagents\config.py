from pathlib import Path
from pydantic import Field

from common.config.base_settings import BaseAgentSettings


class PhiloAgentsSettings(BaseAgentSettings):
    """Settings specific to philosopher agents."""

    # --- MongoDB Collections for PhiloAgents ---
    MONGO_STATE_CHECKPOINT_COLLECTION: str = "philosopher_state_checkpoints"
    MONGO_STATE_WRITES_COLLECTION: str = "philosopher_state_writes"
    MONGO_LONG_TERM_MEMORY_COLLECTION: str = "philosopher_long_term_memory"

    # --- PhiloAgents Specific Configuration ---
    COMET_PROJECT: str = Field(
        default="philoagents_course",
        description="Project name for PhiloAgents tracking.",
    )

    # --- Paths Configuration ---
    EVALUATION_DATASET_FILE_PATH: Path = Path("data/evaluation_dataset.json")
    EXTRACTION_METADATA_FILE_PATH: Path = Path("data/extraction_metadata.json")


settings = PhiloAgentsSettings()
