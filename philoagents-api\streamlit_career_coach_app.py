#!/usr/bin/env python3
"""
Streamlit app for testing Career Coach Agents with interactive chat interface.
"""

import streamlit as st
import asyncio
import sys
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional

# Add src to path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from career_coaches.domain.coach_factory import CoachFactory
    from career_coaches.application.conversation_service.generate_response import get_response
    from career_coaches.config import settings
except ImportError as e:
    st.error(f"Import error: {e}")
    st.error("Make sure you're running this from the philoagents-api directory and have set up the environment correctly.")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="Career Coach Agents",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .coach-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
    }
    .user-message {
        background-color: #e3f2fd;
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 3px solid #2196f3;
    }
    .coach-message {
        background-color: #f3e5f5;
        padding: 0.5rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 3px solid #9c27b0;
    }
    .metrics-card {
        background-color: #fff3e0;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #ff9800;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = {}
    if "user_id" not in st.session_state:
        st.session_state.user_id = f"user_{uuid.uuid4().hex[:8]}"
    if "selected_coach" not in st.session_state:
        st.session_state.selected_coach = None
    if "conversation_active" not in st.session_state:
        st.session_state.conversation_active = False
    if "user_context" not in st.session_state:
        st.session_state.user_context = ""
    if "session_goals" not in st.session_state:
        st.session_state.session_goals = []


def get_coach_info() -> Dict:
    """Get information about all available coaches."""
    try:
        coach_factory = CoachFactory()
        available_coaches = coach_factory.get_available_coaches()
        
        coaches_info = {}
        for coach_id in available_coaches:
            coach = coach_factory.get_coach(coach_id)
            coaches_info[coach_id] = {
                "name": coach.name,
                "specialty": coach.specialty,
                "approach": coach.approach,
                "focus_areas": coach.focus_areas,
                "object": coach
            }
        
        return coaches_info
    except Exception as e:
        st.error(f"Error loading coaches: {e}")
        return {}


def display_coach_card(coach_id: str, coach_info: Dict):
    """Display a coach information card."""
    with st.container():
        st.markdown(f"""
        <div class="coach-card">
            <h4>🎯 {coach_info['name']}</h4>
            <p><strong>Specialty:</strong> {coach_info['specialty']}</p>
            <p><strong>Approach:</strong> {coach_info['approach'][:150]}...</p>
        </div>
        """, unsafe_allow_html=True)
        
        with st.expander(f"View {coach_info['name']}'s Focus Areas"):
            for area in coach_info['focus_areas']:
                st.write(f"• {area}")


async def send_message_to_coach(
    message: str, 
    coach_id: str, 
    user_id: str,
    user_context: str = "",
    session_goals: List[str] = None
) -> Optional[str]:
    """Send a message to the selected coach and get response."""
    try:
        coach_factory = CoachFactory()
        coach = coach_factory.get_coach(coach_id)
        
        if session_goals is None:
            session_goals = []
        
        response, _ = await get_response(
            messages=message,
            user_id=user_id,
            coach_id=coach_id,
            coach_name=coach.name,
            coach_specialty=coach.specialty,
            coach_approach=coach.approach,
            coach_focus_areas=coach.focus_areas,
            user_context=user_context,
            session_goals=session_goals,
        )
        
        return response
    except Exception as e:
        st.error(f"Error getting response from coach: {e}")
        return None


def display_chat_interface():
    """Display the main chat interface."""
    st.header("💬 Chat with Your Career Coach")
    
    # Coach selection
    coaches_info = get_coach_info()
    if not coaches_info:
        st.error("No coaches available. Please check your setup.")
        return
    
    coach_options = {f"{info['name']} - {info['specialty']}": coach_id 
                    for coach_id, info in coaches_info.items()}
    
    selected_coach_display = st.selectbox(
        "Select a Career Coach:",
        options=list(coach_options.keys()),
        key="coach_selector"
    )
    
    if selected_coach_display:
        selected_coach_id = coach_options[selected_coach_display]
        st.session_state.selected_coach = selected_coach_id
        
        # Display selected coach info
        coach_info = coaches_info[selected_coach_id]
        st.info(f"**{coach_info['name']}** - {coach_info['specialty']}")
        
        # User context and goals
        with st.expander("📝 Set Context & Goals (Optional)"):
            st.session_state.user_context = st.text_area(
                "Describe your career situation:",
                value=st.session_state.user_context,
                placeholder="e.g., Recent graduate, 5 years in marketing, looking to transition to tech..."
            )
            
            goals_input = st.text_input(
                "Session goals (comma-separated):",
                placeholder="e.g., identify strengths, explore options, improve resume"
            )
            
            if goals_input:
                st.session_state.session_goals = [goal.strip() for goal in goals_input.split(",")]
            
            if st.session_state.session_goals:
                st.write("**Current Goals:**")
                for goal in st.session_state.session_goals:
                    st.write(f"• {goal}")
        
        # Chat interface
        chat_container = st.container()
        
        # Initialize conversation for this coach if not exists
        conversation_key = f"{st.session_state.user_id}_{selected_coach_id}"
        if conversation_key not in st.session_state.messages:
            st.session_state.messages[conversation_key] = []
        
        # Display chat history
        with chat_container:
            for message in st.session_state.messages[conversation_key]:
                if message["role"] == "user":
                    st.markdown(f"""
                    <div class="user-message">
                        <strong>You:</strong> {message["content"]}
                        <br><small>{message["timestamp"]}</small>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="coach-message">
                        <strong>{coach_info['name']}:</strong> {message["content"]}
                        <br><small>{message["timestamp"]}</small>
                    </div>
                    """, unsafe_allow_html=True)
        
        # Message input
        with st.form(key="message_form", clear_on_submit=True):
            user_message = st.text_area(
                "Your message:",
                placeholder=f"Ask {coach_info['name']} for career guidance...",
                height=100
            )
            
            col1, col2, col3 = st.columns([1, 1, 2])
            
            with col1:
                send_button = st.form_submit_button("Send Message", type="primary")
            
            with col2:
                clear_button = st.form_submit_button("Clear Chat")
            
            if send_button and user_message:
                # Add user message to chat
                timestamp = datetime.now().strftime("%H:%M:%S")
                st.session_state.messages[conversation_key].append({
                    "role": "user",
                    "content": user_message,
                    "timestamp": timestamp
                })
                
                # Get coach response
                with st.spinner(f"Getting response from {coach_info['name']}..."):
                    try:
                        response = asyncio.run(send_message_to_coach(
                            user_message,
                            selected_coach_id,
                            st.session_state.user_id,
                            st.session_state.user_context,
                            st.session_state.session_goals
                        ))
                        
                        if response:
                            # Add coach response to chat
                            st.session_state.messages[conversation_key].append({
                                "role": "assistant",
                                "content": response,
                                "timestamp": datetime.now().strftime("%H:%M:%S")
                            })
                            st.rerun()
                        else:
                            st.error("Failed to get response from coach. Please check your configuration.")
                    
                    except Exception as e:
                        st.error(f"Error: {e}")
                        st.error("Make sure MongoDB is running and environment variables are set correctly.")
            
            if clear_button:
                st.session_state.messages[conversation_key] = []
                st.rerun()


def display_sidebar():
    """Display the sidebar with user info and controls."""
    with st.sidebar:
        st.header("🎯 Career Coach Agents")
        
        # User information
        st.subheader("👤 User Information")
        st.write(f"**User ID:** `{st.session_state.user_id}`")
        
        if st.button("🔄 Generate New User ID"):
            st.session_state.user_id = f"user_{uuid.uuid4().hex[:8]}"
            st.session_state.messages = {}
            st.rerun()
        
        # System status
        st.subheader("🔧 System Status")
        
        try:
            coaches_info = get_coach_info()
            if coaches_info:
                st.success(f"✅ {len(coaches_info)} coaches available")
            else:
                st.error("❌ No coaches available")
        except Exception as e:
            st.error(f"❌ System error: {e}")
        
        # Configuration check
        st.subheader("⚙️ Configuration")
        
        config_checks = [
            ("GROQ_API_KEY", hasattr(settings, 'GROQ_API_KEY') and settings.GROQ_API_KEY),
            ("MongoDB URI", hasattr(settings, 'MONGO_URI') and settings.MONGO_URI),
            ("Database Name", hasattr(settings, 'MONGO_DB_NAME') and settings.MONGO_DB_NAME),
        ]
        
        for check_name, check_result in config_checks:
            if check_result:
                st.success(f"✅ {check_name}")
            else:
                st.error(f"❌ {check_name}")
        
        # Quick actions
        st.subheader("🚀 Quick Actions")
        
        if st.button("📋 View All Coaches"):
            st.session_state.show_coaches = True
        
        if st.button("🧹 Clear All Conversations"):
            st.session_state.messages = {}
            st.success("All conversations cleared!")
        
        # Statistics
        st.subheader("📊 Session Statistics")
        total_conversations = len(st.session_state.messages)
        total_messages = sum(len(msgs) for msgs in st.session_state.messages.values())
        
        st.metric("Active Conversations", total_conversations)
        st.metric("Total Messages", total_messages)


def display_coaches_overview():
    """Display an overview of all available coaches."""
    st.header("👥 Available Career Coaches")
    
    coaches_info = get_coach_info()
    
    if not coaches_info:
        st.error("No coaches available. Please check your setup.")
        return
    
    # Create columns for coach cards
    cols = st.columns(2)
    
    for i, (coach_id, coach_info) in enumerate(coaches_info.items()):
        with cols[i % 2]:
            display_coach_card(coach_id, coach_info)


def main():
    """Main application function."""
    initialize_session_state()
    
    # Display sidebar
    display_sidebar()
    
    # Main content
    st.title("🎯 Career Coach Agents - Interactive Testing")
    st.markdown("Test and interact with specialized AI career coaches in real-time.")
    
    # Navigation tabs
    tab1, tab2, tab3 = st.tabs(["💬 Chat", "👥 Coaches", "📊 Analytics"])
    
    with tab1:
        display_chat_interface()
    
    with tab2:
        display_coaches_overview()
    
    with tab3:
        st.header("📊 Conversation Analytics")
        
        if st.session_state.messages:
            # Conversation statistics
            st.subheader("📈 Statistics")
            
            conversation_data = []
            for conv_key, messages in st.session_state.messages.items():
                user_id, coach_id = conv_key.split("_", 1)
                conversation_data.append({
                    "Coach": coach_id.replace("_", " ").title(),
                    "Messages": len(messages),
                    "User Messages": len([m for m in messages if m["role"] == "user"]),
                    "Coach Responses": len([m for m in messages if m["role"] == "assistant"])
                })
            
            if conversation_data:
                import pandas as pd
                df = pd.DataFrame(conversation_data)
                st.dataframe(df, use_container_width=True)
                
                # Charts
                col1, col2 = st.columns(2)
                
                with col1:
                    st.bar_chart(df.set_index("Coach")["Messages"])
                
                with col2:
                    coach_usage = df.groupby("Coach")["Messages"].sum()
                    st.bar_chart(coach_usage)
        else:
            st.info("No conversations yet. Start chatting with a coach to see analytics!")


if __name__ == "__main__":
    main()
