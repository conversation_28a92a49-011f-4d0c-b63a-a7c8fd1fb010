# Career Coach Agents - Streamlit UI

A comprehensive web interface for testing and interacting with the Career Coach Agents system. Built with Streamlit, this UI provides an intuitive chat interface, analytics dashboard, and management tools.

## 🌟 Features

### 💬 Interactive Chat Interface
- **Real-time Conversations**: Stream responses from career coaches
- **Multi-User Support**: Each user maintains separate conversation history
- **Coach Selection**: Choose from 4 specialized career coaches
- **Context Management**: Add user context for personalized guidance
- **Conversation Export**: Download chat history as JSON

### 👥 Coach Profiles
- **Detailed Information**: Learn about each coach's specialty and approach
- **Sample Questions**: Get conversation starters for each coach
- **Quick Comparison**: Compare coaches side-by-side

### 📊 Analytics Dashboard
- **Usage Metrics**: Track conversations, messages, and user activity
- **Coach Performance**: Monitor satisfaction scores and response times
- **Trend Analysis**: Visualize usage patterns over time
- **Detailed Statistics**: Comprehensive performance data

### ⚙️ Settings & Configuration
- **API Configuration**: Customize API endpoints and timeouts
- **User Management**: Manage sessions and reset memory
- **Data Import/Export**: Backup and restore conversation data
- **System Information**: Monitor application status

## 🚀 Quick Start

### Option 1: Automated Startup (Recommended)

```bash
# Navigate to the UI directory
cd ui

# Run the startup script (installs dependencies and starts everything)
python run_streamlit.py
```

### Option 2: Manual Setup

```bash
# 1. Install dependencies
cd ui
pip install -r requirements.txt

# 2. Start the API server (in another terminal)
cd ../philoagents-api
python src/main.py

# 3. Start the Streamlit app
cd ../ui
streamlit run streamlit_app.py
```

### 🌐 Access the Application

- **Streamlit UI**: http://localhost:8501
- **API Server**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📱 Application Structure

```
ui/
├── streamlit_app.py          # Main application and chat interface
├── pages/
│   ├── 1_🎯_Chat.py         # Chat interface (main page)
│   ├── 2_👥_Coaches.py      # Coach profiles and information
│   ├── 3_📊_Analytics.py    # Analytics dashboard
│   └── 4_⚙️_Settings.py    # Settings and configuration
├── run_streamlit.py          # Automated startup script
├── requirements.txt          # Python dependencies
└── README.md                # This file
```

## 🎯 Available Coaches

### Dr. Sarah Chen - Career Assessment
- **Specialty**: Career Assessment & Path Planning
- **Best For**: Career exploration, skills analysis, goal setting
- **Sample Question**: "I'm feeling lost in my career direction. Can you help?"

### Marcus Rodriguez - Resume Builder
- **Specialty**: Resume Writing & ATS Optimization
- **Best For**: Job applications, resume optimization, ATS compliance
- **Sample Question**: "My resume isn't getting me interviews. What's wrong?"

### Emma Thompson - LinkedIn Optimizer
- **Specialty**: LinkedIn Profile & Personal Branding
- **Best For**: Personal branding, content strategy, networking
- **Sample Question**: "How do I create a LinkedIn headline that stands out?"

### David Kim - Networking Strategy
- **Specialty**: Professional Networking & Relationship Building
- **Best For**: Networking events, relationship building, introvert strategies
- **Sample Question**: "I'm an introvert. How can I network effectively?"

## 💬 Using the Chat Interface

### Starting a Conversation

1. **Select a Coach**: Choose from the sidebar dropdown
2. **Set User Context**: Add background information about your situation
3. **Start Chatting**: Type your message and press Enter or click Send
4. **View Responses**: Watch real-time streaming responses from your coach

### Chat Features

- **Streaming Responses**: See responses as they're generated
- **Conversation Memory**: Coaches remember your conversation history
- **Multi-User Support**: Each user ID maintains separate conversations
- **Export Conversations**: Download your chat history
- **Reset Memory**: Clear conversation history when needed

### User Context Examples

```
Recent graduate with computer science degree, uncertain about career path

Marketing professional with 5 years experience, looking to transition to product management

Entrepreneur starting a consulting business, need to build LinkedIn presence

Introvert seeking networking strategies for career advancement
```

## 📊 Analytics Features

### Overview Metrics
- Total conversations across all coaches
- Message counts and user activity
- Average satisfaction scores
- Active user statistics

### Coach Performance
- Conversations by coach comparison
- Satisfaction scores by coach
- Average session duration
- Message volume analysis

### Usage Trends
- Daily conversation trends
- Peak usage times
- User engagement patterns
- Growth metrics

## ⚙️ Configuration Options

### API Settings
- **Base URL**: Configure API endpoint (default: http://localhost:8000)
- **Timeout**: Set request timeout (5-60 seconds)
- **Auto-retry**: Enable automatic retry for failed requests

### User Management
- **User ID**: Unique identifier for conversation isolation
- **Memory Reset**: Clear conversation history for current user
- **Admin Functions**: Reset memory for all users (use with caution)

### UI Preferences
- **Theme**: Auto, Light, or Dark mode
- **Chat Style**: Bubbles, Simple, or Compact
- **Auto-scroll**: Automatically scroll to new messages
- **Timestamps**: Show message timestamps

## 🔧 Troubleshooting

### Common Issues

#### API Connection Failed
```
❌ API Disconnected
```
**Solution**: 
1. Ensure the API server is running: `python src/main.py`
2. Check the API URL in Settings (should be http://localhost:8000)
3. Verify no firewall is blocking the connection

#### Coach Not Responding
```
Failed to get response from coach
```
**Solution**:
1. Check API server logs for errors
2. Verify your API keys are configured in `.env`
3. Try resetting the conversation memory

#### Streamlit App Won't Start
```
ModuleNotFoundError: No module named 'streamlit'
```
**Solution**:
1. Install dependencies: `pip install -r requirements.txt`
2. Use Python 3.8 or higher
3. Consider using a virtual environment

### Debug Mode

Enable debug logging by setting environment variable:
```bash
export STREAMLIT_LOGGER_LEVEL=debug
streamlit run streamlit_app.py
```

## 🔒 Security Considerations

### Multi-User Environment
- Each user ID maintains separate conversation history
- No cross-user data leakage
- Session isolation enforced at API level

### Data Privacy
- Conversations are stored temporarily in session state
- Export feature allows users to control their data
- Memory reset functionality for data cleanup

## 🚀 Development

### Adding New Features

1. **New Pages**: Add to `pages/` directory with numbered prefix
2. **Custom Components**: Extend `streamlit_app.py` with new functions
3. **API Integration**: Use the existing request patterns for consistency

### Customization

- **Styling**: Modify CSS in the `st.markdown()` sections
- **Layout**: Adjust column layouts and component arrangements
- **Functionality**: Add new API endpoints and corresponding UI elements

## 📝 Dependencies

- **streamlit**: Web application framework
- **requests**: HTTP client for API communication
- **plotly**: Interactive charts and visualizations
- **pandas**: Data manipulation and analysis

## 🤝 Contributing

1. Follow the existing code structure and naming conventions
2. Test all new features with the API server
3. Update documentation for new functionality
4. Ensure multi-user support is maintained

## 📄 License

Same license as the parent Career Coach Agents project.

---

**Happy Coaching! 🎯**

For more information about the Career Coach Agents system, see the main project documentation.
