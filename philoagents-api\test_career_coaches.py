#!/usr/bin/env python3
"""
Simple test script to verify career coach system functionality.
"""

import asyncio
import sys
import os

# Add src to path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from career_coaches.domain.coach_factory import CoachFactory
from career_coaches.application.conversation_service.generate_response import get_response


async def test_career_coach():
    """Test basic career coach functionality."""
    
    print("🧪 Testing Career Coach System")
    print("=" * 50)
    
    # Test 1: Coach Factory
    print("\n1. Testing Coach Factory...")
    try:
        coach_factory = CoachFactory()
        available_coaches = coach_factory.get_available_coaches()
        print(f"✅ Available coaches: {available_coaches}")
        
        # Test getting a specific coach
        coach = coach_factory.get_coach("career_assessment")
        print(f"✅ Career Assessment Coach: {coach.name}")
        print(f"   Specialty: {coach.specialty}")
        print(f"   Focus Areas: {len(coach.focus_areas)} areas")
        
    except Exception as e:
        print(f"❌ Coach Factory test failed: {e}")
        return False
    
    # Test 2: Basic Conversation (without MongoDB - will fail gracefully)
    print("\n2. Testing Basic Conversation Structure...")
    try:
        # This will likely fail due to MongoDB connection, but we can test the structure
        user_id = "test_user_001"
        coach_id = "career_assessment"
        message = "Hi, I need help with my career direction."
        
        coach = coach_factory.get_coach(coach_id)
        
        print(f"✅ Conversation setup successful")
        print(f"   User ID: {user_id}")
        print(f"   Coach: {coach.name}")
        print(f"   Message: {message}")
        
        # Note: Actual conversation test would require MongoDB setup
        print("   (Actual conversation requires MongoDB setup)")
        
    except Exception as e:
        print(f"⚠️  Conversation test structure check: {e}")
    
    # Test 3: Multi-user Thread ID Generation
    print("\n3. Testing Multi-user Thread ID Logic...")
    try:
        user_ids = ["user_001", "user_002", "user_003"]
        coach_ids = ["career_assessment", "resume_builder"]
        
        for user_id in user_ids:
            for coach_id in coach_ids:
                thread_id = f"{user_id}_{coach_id}"
                print(f"   Thread ID: {thread_id}")
        
        print("✅ Multi-user thread ID generation working")
        
    except Exception as e:
        print(f"❌ Thread ID test failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Career Coach System Basic Tests Completed!")
    print("\nNext steps:")
    print("1. Set up MongoDB connection")
    print("2. Configure environment variables (.env file)")
    print("3. Run: python tools/call_career_coach.py --coach-id career_assessment --user-id test_user --query 'Hello'")
    
    return True


def test_imports():
    """Test that all imports work correctly."""
    
    print("📦 Testing Imports...")
    print("-" * 30)
    
    try:
        # Test common imports
        from common.config.base_settings import BaseAgentSettings
        print("✅ Common config imported")
        
        from common.domain.exceptions import CoachNameNotFound
        print("✅ Common exceptions imported")
        
        from common.domain.prompts import Prompt
        print("✅ Common prompts imported")
        
        # Test career coach imports
        from career_coaches.config import settings
        print("✅ Career coach config imported")
        
        from career_coaches.domain.coach import Coach
        print("✅ Career coach domain imported")
        
        from career_coaches.domain.coach_factory import CoachFactory
        print("✅ Career coach factory imported")
        
        from career_coaches.domain.prompts import CAREER_COACH_CHARACTER_CARD
        print("✅ Career coach prompts imported")
        
        print("\n✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Career Coach System Test Suite")
    print("=" * 60)
    
    # Test imports first
    if not test_imports():
        print("❌ Import tests failed. Please check your setup.")
        sys.exit(1)
    
    # Test basic functionality
    try:
        result = asyncio.run(test_career_coach())
        if result:
            print("\n✅ All tests passed!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed.")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
