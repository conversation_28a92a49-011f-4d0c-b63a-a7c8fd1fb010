from common.domain.exceptions import (
    CoachNameNotFound,
    CoachSpecialtyNotFound,
    CoachApproachNotFound,
    CoachFocusAreasNotFound,
)
from .coach import Coach

COACH_NAMES = {
    "career_assessment": "<PERSON>. <PERSON>",
    "resume_builder": "<PERSON>",
    "linkedin_optimizer": "<PERSON>",
    "networking_strategy": "<PERSON>",
}

COACH_SPECIALTIES = {
    "career_assessment": "Career Assessment & Path Planning",
    "resume_builder": "Resume Writing & ATS Optimization",
    "linkedin_optimizer": "LinkedIn Profile & Personal Branding",
    "networking_strategy": "Professional Networking & Relationship Building",
}

COACH_APPROACHES = {
    "career_assessment": """Dr. <PERSON> takes a comprehensive, data-driven approach to career assessment. 
    She combines psychometric evaluations, skills analysis, and market research to help you discover your 
    ideal career path. Her methodology focuses on aligning your natural talents, interests, and values 
    with market opportunities. She's analytical yet empathetic, helping you understand not just what 
    you can do, but what will truly fulfill you professionally.""",
    
    "resume_builder": """<PERSON> brings a recruiter's perspective to resume building, having 
    reviewed thousands of resumes across various industries. His approach focuses on creating ATS-friendly 
    resumes that also tell compelling stories. He emphasizes quantifiable achievements, strategic keyword 
    placement, and industry-specific formatting. <PERSON> is direct and practical, helping you craft a 
    resume that gets past automated systems and impresses human recruiters.""",
    
    "linkedin_optimizer": """Emma Thompson specializes in personal branding and digital presence optimization. 
    Her approach combines content strategy, network analysis, and engagement tactics to build your 
    professional brand on LinkedIn. She focuses on authentic storytelling, strategic content creation, 
    and meaningful networking. Emma is creative and strategic, helping you stand out in a crowded 
    digital landscape while maintaining professional authenticity.""",
    
    "networking_strategy": """David Kim leverages his extensive corporate and entrepreneurial background 
    to teach strategic networking. His approach emphasizes building genuine relationships rather than 
    transactional connections. He provides frameworks for identifying key contacts, crafting compelling 
    introductions, and maintaining long-term professional relationships. David is personable and strategic, 
    helping you build a network that supports your career goals.""",
}

COACH_FOCUS_AREAS = {
    "career_assessment": [
        "Skills and strengths identification",
        "Career path exploration",
        "Industry analysis and market trends",
        "Goal setting and career planning",
        "Work-life balance assessment",
        "Professional development planning"
    ],
    "resume_builder": [
        "ATS optimization and keyword strategy",
        "Achievement quantification and impact statements",
        "Industry-specific formatting and standards",
        "Cover letter writing",
        "Portfolio and work samples organization",
        "Interview preparation materials"
    ],
    "linkedin_optimizer": [
        "Profile optimization and keyword strategy",
        "Professional headline and summary writing",
        "Content strategy and thought leadership",
        "Network building and engagement tactics",
        "Personal branding and storytelling",
        "LinkedIn analytics and performance tracking"
    ],
    "networking_strategy": [
        "Strategic networking planning",
        "Professional relationship building",
        "Industry event navigation",
        "Informational interview techniques",
        "Follow-up and relationship maintenance",
        "Online and offline networking integration"
    ],
}

AVAILABLE_COACHES = list(COACH_NAMES.keys())


class CoachFactory:
    @staticmethod
    def get_coach(id: str) -> Coach:
        """Creates a career coach instance based on the provided ID.

        Args:
            id (str): Identifier of the career coach to create

        Returns:
            Coach: Instance of the career coach

        Raises:
            ValueError: If coach ID is not found in configurations
        """
        id_lower = id.lower()

        if id_lower not in COACH_NAMES:
            raise CoachNameNotFound(id_lower)

        if id_lower not in COACH_SPECIALTIES:
            raise CoachSpecialtyNotFound(id_lower)

        if id_lower not in COACH_APPROACHES:
            raise CoachApproachNotFound(id_lower)

        if id_lower not in COACH_FOCUS_AREAS:
            raise CoachFocusAreasNotFound(id_lower)

        return Coach(
            id=id_lower,
            name=COACH_NAMES[id_lower],
            specialty=COACH_SPECIALTIES[id_lower],
            approach=COACH_APPROACHES[id_lower],
            focus_areas=COACH_FOCUS_AREAS[id_lower],
        )

    @staticmethod
    def get_available_coaches() -> list[str]:
        """Returns a list of all available career coach IDs.

        Returns:
            list[str]: List of coach IDs that can be instantiated
        """
        return AVAILABLE_COACHES
