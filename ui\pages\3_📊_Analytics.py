import streamlit as st
import requests
import json
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta
import time

# Page configuration
st.set_page_config(
    page_title="Analytics Dashboard",
    page_icon="📊",
    layout="wide"
)

# Configuration
API_BASE_URL = "http://localhost:8000"

# Initialize session state for analytics data
if "analytics_data" not in st.session_state:
    st.session_state.analytics_data = {
        "conversations": [],
        "coach_usage": {},
        "user_activity": {},
        "response_times": []
    }


def generate_mock_analytics_data():
    """Generate mock analytics data for demonstration."""
    import random
    from datetime import datetime, timedelta
    
    coaches = ["career_assessment", "resume_builder", "linkedin_optimizer", "networking_strategy"]
    coach_names = {
        "career_assessment": "<PERSON>. <PERSON>",
        "resume_builder": "<PERSON>", 
        "linkedin_optimizer": "<PERSON>",
        "networking_strategy": "<PERSON>"
    }
    
    # Generate mock conversation data
    conversations = []
    for i in range(50):
        coach_id = random.choice(coaches)
        conversations.append({
            "id": f"conv_{i}",
            "coach_id": coach_id,
            "coach_name": coach_names[coach_id],
            "user_id": f"user_{random.randint(1, 20):03d}",
            "timestamp": datetime.now() - timedelta(days=random.randint(0, 30)),
            "message_count": random.randint(3, 15),
            "duration_minutes": random.randint(5, 45),
            "satisfaction": random.uniform(3.5, 5.0)
        })
    
    # Coach usage statistics
    coach_usage = {}
    for coach_id in coaches:
        coach_conversations = [c for c in conversations if c["coach_id"] == coach_id]
        coach_usage[coach_id] = {
            "name": coach_names[coach_id],
            "total_conversations": len(coach_conversations),
            "total_messages": sum(c["message_count"] for c in coach_conversations),
            "avg_satisfaction": sum(c["satisfaction"] for c in coach_conversations) / len(coach_conversations) if coach_conversations else 0,
            "avg_duration": sum(c["duration_minutes"] for c in coach_conversations) / len(coach_conversations) if coach_conversations else 0
        }
    
    return {
        "conversations": conversations,
        "coach_usage": coach_usage,
        "last_updated": datetime.now()
    }


def display_overview_metrics(data):
    """Display overview metrics."""
    conversations = data["conversations"]
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Total Conversations",
            len(conversations),
            delta=f"+{len([c for c in conversations if c['timestamp'] > datetime.now() - timedelta(days=7)])}"
        )
    
    with col2:
        total_messages = sum(c["message_count"] for c in conversations)
        st.metric("Total Messages", total_messages)
    
    with col3:
        avg_satisfaction = sum(c["satisfaction"] for c in conversations) / len(conversations) if conversations else 0
        st.metric("Avg Satisfaction", f"{avg_satisfaction:.1f}/5.0")
    
    with col4:
        unique_users = len(set(c["user_id"] for c in conversations))
        st.metric("Active Users", unique_users)


def display_coach_performance(data):
    """Display coach performance charts."""
    coach_usage = data["coach_usage"]
    
    # Coach usage comparison
    coach_names = [coach["name"] for coach in coach_usage.values()]
    conversation_counts = [coach["total_conversations"] for coach in coach_usage.values()]
    satisfaction_scores = [coach["avg_satisfaction"] for coach in coach_usage.values()]
    
    col1, col2 = st.columns(2)
    
    with col1:
        fig_usage = px.bar(
            x=coach_names,
            y=conversation_counts,
            title="Conversations by Coach",
            labels={"x": "Coach", "y": "Number of Conversations"},
            color=conversation_counts,
            color_continuous_scale="viridis"
        )
        fig_usage.update_layout(showlegend=False)
        st.plotly_chart(fig_usage, use_container_width=True)
    
    with col2:
        fig_satisfaction = px.bar(
            x=coach_names,
            y=satisfaction_scores,
            title="Average Satisfaction by Coach",
            labels={"x": "Coach", "y": "Satisfaction Score"},
            color=satisfaction_scores,
            color_continuous_scale="RdYlGn",
            range_y=[0, 5]
        )
        fig_satisfaction.update_layout(showlegend=False)
        st.plotly_chart(fig_satisfaction, use_container_width=True)


def display_usage_trends(data):
    """Display usage trends over time."""
    conversations = data["conversations"]
    
    # Group conversations by date
    daily_counts = {}
    for conv in conversations:
        date = conv["timestamp"].date()
        daily_counts[date] = daily_counts.get(date, 0) + 1
    
    # Create DataFrame for plotting
    dates = sorted(daily_counts.keys())
    counts = [daily_counts[date] for date in dates]
    
    df = pd.DataFrame({
        "Date": dates,
        "Conversations": counts
    })
    
    fig = px.line(
        df,
        x="Date",
        y="Conversations",
        title="Daily Conversation Trends",
        markers=True
    )
    fig.update_layout(
        xaxis_title="Date",
        yaxis_title="Number of Conversations"
    )
    
    st.plotly_chart(fig, use_container_width=True)


def display_detailed_stats(data):
    """Display detailed statistics table."""
    coach_usage = data["coach_usage"]
    
    # Create detailed stats DataFrame
    stats_data = []
    for coach_id, stats in coach_usage.items():
        stats_data.append({
            "Coach": stats["name"],
            "Conversations": stats["total_conversations"],
            "Total Messages": stats["total_messages"],
            "Avg Messages/Conv": f"{stats['total_messages'] / stats['total_conversations']:.1f}" if stats['total_conversations'] > 0 else "0",
            "Avg Duration (min)": f"{stats['avg_duration']:.1f}",
            "Satisfaction": f"{stats['avg_satisfaction']:.2f}/5.0"
        })
    
    df = pd.DataFrame(stats_data)
    st.dataframe(df, use_container_width=True)


def main():
    """Main analytics dashboard."""
    st.title("📊 Career Coach Analytics Dashboard")
    st.markdown("Monitor usage patterns, coach performance, and user engagement metrics.")
    
    # Refresh button
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button("🔄 Refresh Data"):
            st.session_state.analytics_data = generate_mock_analytics_data()
            st.rerun()
    
    with col2:
        auto_refresh = st.checkbox("Auto-refresh (30s)")
    
    # Generate initial data if not exists
    if not st.session_state.analytics_data.get("conversations"):
        with st.spinner("Loading analytics data..."):
            st.session_state.analytics_data = generate_mock_analytics_data()
    
    data = st.session_state.analytics_data
    
    # Display last updated time
    if "last_updated" in data:
        st.caption(f"Last updated: {data['last_updated'].strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Overview metrics
    st.markdown("## 📈 Overview")
    display_overview_metrics(data)
    
    st.markdown("---")
    
    # Coach performance
    st.markdown("## 🎯 Coach Performance")
    display_coach_performance(data)
    
    st.markdown("---")
    
    # Usage trends
    st.markdown("## 📅 Usage Trends")
    display_usage_trends(data)
    
    st.markdown("---")
    
    # Detailed statistics
    st.markdown("## 📋 Detailed Statistics")
    display_detailed_stats(data)
    
    # Auto-refresh functionality
    if auto_refresh:
        time.sleep(30)
        st.rerun()
    
    # Note about mock data
    st.markdown("---")
    st.info("📝 **Note**: This dashboard currently displays mock data for demonstration purposes. In a production environment, this would connect to real analytics data from your conversation logs and user interactions.")


if __name__ == "__main__":
    main()
