import streamlit as st
import requests
import json
import os

# Page configuration
st.set_page_config(
    page_title="Settings",
    page_icon="⚙️",
    layout="wide"
)

# Configuration
API_BASE_URL = "http://localhost:8000"
CAREER_COACHES_API = f"{API_BASE_URL}/career-coaches"

# Initialize session state
if "api_settings" not in st.session_state:
    st.session_state.api_settings = {
        "api_url": API_BASE_URL,
        "timeout": 30,
        "auto_retry": True
    }


def test_api_connection(url):
    """Test API connection."""
    try:
        response = requests.get(f"{url}/health", timeout=5)
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except requests.exceptions.RequestException as e:
        return False, str(e)


def get_api_info(url):
    """Get API information."""
    try:
        response = requests.get(f"{url}/services", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except requests.exceptions.RequestException:
        return None


def reset_all_memory():
    """Reset memory for all users."""
    try:
        response = requests.post(f"{CAREER_COACHES_API}/reset-memory", json={}, timeout=10)
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except requests.exceptions.RequestException as e:
        return False, str(e)


def main():
    """Main settings page."""
    st.title("⚙️ Settings & Configuration")
    st.markdown("Configure your Career Coach Agents experience and manage system settings.")
    
    # API Configuration
    st.markdown("## 🔌 API Configuration")
    
    with st.expander("API Connection Settings", expanded=True):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            api_url = st.text_input(
                "API Base URL",
                value=st.session_state.api_settings["api_url"],
                help="Base URL for the Career Coach API"
            )
            
            timeout = st.slider(
                "Request Timeout (seconds)",
                min_value=5,
                max_value=60,
                value=st.session_state.api_settings["timeout"],
                help="Timeout for API requests"
            )
            
            auto_retry = st.checkbox(
                "Auto-retry failed requests",
                value=st.session_state.api_settings["auto_retry"],
                help="Automatically retry failed API requests"
            )
        
        with col2:
            st.markdown("### Connection Test")
            if st.button("🔍 Test Connection"):
                with st.spinner("Testing connection..."):
                    success, result = test_api_connection(api_url)
                    
                    if success:
                        st.success("✅ Connection successful!")
                        if result:
                            st.json(result)
                    else:
                        st.error(f"❌ Connection failed: {result}")
            
            # Save settings
            if st.button("💾 Save Settings"):
                st.session_state.api_settings = {
                    "api_url": api_url,
                    "timeout": timeout,
                    "auto_retry": auto_retry
                }
                st.success("Settings saved!")
    
    # API Information
    st.markdown("## 📋 API Information")
    
    with st.expander("Service Details"):
        api_info = get_api_info(api_url)
        if api_info:
            st.json(api_info)
        else:
            st.warning("Could not retrieve API information. Check your connection.")
    
    # User Management
    st.markdown("## 👤 User Management")
    
    with st.expander("Session Management"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### Current Session")
            if "user_id" in st.session_state:
                st.info(f"User ID: `{st.session_state.user_id}`")
                
                if st.button("🔄 Generate New User ID"):
                    import uuid
                    st.session_state.user_id = f"user_{str(uuid.uuid4())[:8]}"
                    st.success("New User ID generated!")
                    st.rerun()
            else:
                st.warning("No active session found.")
        
        with col2:
            st.markdown("### Reset Options")
            
            # Reset current user memory
            if "user_id" in st.session_state:
                if st.button("🗑️ Reset My Memory"):
                    try:
                        response = requests.post(
                            f"{CAREER_COACHES_API}/reset-memory",
                            json={"user_id": st.session_state.user_id},
                            timeout=10
                        )
                        if response.status_code == 200:
                            st.success("Your conversation memory has been reset!")
                            if "messages" in st.session_state:
                                st.session_state.messages = []
                        else:
                            st.error("Failed to reset memory.")
                    except requests.exceptions.RequestException as e:
                        st.error(f"Error: {e}")
            
            # Reset all memory (admin function)
            st.markdown("#### ⚠️ Admin Functions")
            if st.button("🚨 Reset ALL Memory", type="secondary"):
                st.warning("This will reset conversation memory for ALL users!")
                if st.button("⚠️ Confirm Reset ALL", type="secondary"):
                    success, result = reset_all_memory()
                    if success:
                        st.success("All conversation memory has been reset!")
                    else:
                        st.error(f"Failed to reset memory: {result}")
    
    # Application Settings
    st.markdown("## 🎨 Application Settings")
    
    with st.expander("UI Preferences"):
        col1, col2 = st.columns(2)
        
        with col1:
            theme = st.selectbox(
                "Theme",
                options=["Auto", "Light", "Dark"],
                index=0,
                help="Choose your preferred theme"
            )
            
            chat_style = st.selectbox(
                "Chat Style",
                options=["Bubbles", "Simple", "Compact"],
                index=0,
                help="Choose chat message display style"
            )
        
        with col2:
            auto_scroll = st.checkbox(
                "Auto-scroll chat",
                value=True,
                help="Automatically scroll to new messages"
            )
            
            show_timestamps = st.checkbox(
                "Show message timestamps",
                value=False,
                help="Display timestamps for each message"
            )
    
    # Export/Import Settings
    st.markdown("## 📁 Data Management")
    
    with st.expander("Export & Import"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### Export Data")
            
            if "messages" in st.session_state and st.session_state.messages:
                export_data = {
                    "user_id": st.session_state.get("user_id", "unknown"),
                    "settings": st.session_state.api_settings,
                    "messages": st.session_state.messages,
                    "export_timestamp": str(pd.Timestamp.now())
                }
                
                st.download_button(
                    label="📥 Export All Data",
                    data=json.dumps(export_data, indent=2),
                    file_name=f"career_coach_data_{st.session_state.get('user_id', 'unknown')}.json",
                    mime="application/json"
                )
            else:
                st.info("No conversation data to export.")
        
        with col2:
            st.markdown("### Import Data")
            
            uploaded_file = st.file_uploader(
                "Choose a JSON file",
                type="json",
                help="Upload previously exported data"
            )
            
            if uploaded_file is not None:
                try:
                    import_data = json.load(uploaded_file)
                    
                    if st.button("📤 Import Data"):
                        # Import settings
                        if "settings" in import_data:
                            st.session_state.api_settings = import_data["settings"]
                        
                        # Import messages
                        if "messages" in import_data:
                            st.session_state.messages = import_data["messages"]
                        
                        # Import user ID
                        if "user_id" in import_data:
                            st.session_state.user_id = import_data["user_id"]
                        
                        st.success("Data imported successfully!")
                        st.rerun()
                        
                except json.JSONDecodeError:
                    st.error("Invalid JSON file.")
    
    # System Information
    st.markdown("## 🔧 System Information")
    
    with st.expander("Environment Details"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### Application")
            st.code(f"""
Streamlit Version: {st.__version__}
Python Version: {os.sys.version.split()[0]}
Platform: {os.name}
            """)
        
        with col2:
            st.markdown("### Session State")
            session_info = {
                "User ID": st.session_state.get("user_id", "Not set"),
                "Messages": len(st.session_state.get("messages", [])),
                "Selected Coach": st.session_state.get("selected_coach", "None"),
                "Conversation Started": st.session_state.get("conversation_started", False)
            }
            
            for key, value in session_info.items():
                st.text(f"{key}: {value}")
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>⚙️ Career Coach Agents Settings | 
        <a href="http://localhost:8000/docs" target="_blank">API Documentation</a></p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    import pandas as pd
    main()
