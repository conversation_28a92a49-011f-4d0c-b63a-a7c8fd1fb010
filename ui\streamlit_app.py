import streamlit as st
import requests
import json
import time
import uuid
from typing import Dict, List, Optional

# Page configuration
st.set_page_config(
    page_title="Career Coach Agents",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .coach-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin: 0.5rem 0;
    }

    .user-message {
        background: #e3f2fd;
        padding: 0.8rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #2196f3;
    }

    .coach-message {
        background: #f3e5f5;
        padding: 0.8rem;
        border-radius: 10px;
        margin: 0.5rem 0;
        border-left: 4px solid #9c27b0;
    }

    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Configuration
API_BASE_URL = "http://localhost:8000"
CAREER_COACHES_API = f"{API_BASE_URL}/career-coaches"

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "user_id" not in st.session_state:
    st.session_state.user_id = f"user_{str(uuid.uuid4())[:8]}"
if "selected_coach" not in st.session_state:
    st.session_state.selected_coach = None
if "coaches_data" not in st.session_state:
    st.session_state.coaches_data = {}
if "conversation_started" not in st.session_state:
    st.session_state.conversation_started = False


def load_coaches() -> Dict:
    """Load available coaches from the API."""
    try:
        response = requests.get(f"{CAREER_COACHES_API}/coaches", timeout=5)
        if response.status_code == 200:
            return response.json().get("coaches", [])
        else:
            st.error(f"Failed to load coaches: {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        st.error(f"Could not connect to Career Coaches API: {e}")
        st.info("Make sure the API server is running on http://localhost:8000")
        return []


def send_message(message: str, coach_id: str, user_id: str, user_context: str = "") -> Optional[str]:
    """Send a message to the career coach API."""
    try:
        payload = {
            "message": message,
            "coach_id": coach_id,
            "user_id": user_id,
            "user_context": user_context,
            "session_goals": []
        }

        response = requests.post(
            f"{CAREER_COACHES_API}/chat",
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            return response.json().get("response", "No response received")
        else:
            st.error(f"API Error: {response.status_code} - {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        st.error(f"Connection error: {e}")
        return None


def reset_conversation(user_id: str) -> bool:
    """Reset the conversation memory for a user."""
    try:
        payload = {"user_id": user_id}
        response = requests.post(
            f"{CAREER_COACHES_API}/reset-memory",
            json=payload,
            timeout=10
        )
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False


def display_coach_card(coach: Dict):
    """Display a coach information card."""
    with st.container():
        st.markdown(f"""
        <div class="coach-card">
            <h4>🎯 {coach['name']}</h4>
            <p><strong>Specialty:</strong> {coach['specialty']}</p>
            <p><strong>Focus Areas:</strong></p>
            <ul>
        """, unsafe_allow_html=True)

        for area in coach['focus_areas'][:3]:  # Show first 3 focus areas
            st.markdown(f"<li>{area}</li>", unsafe_allow_html=True)

        if len(coach['focus_areas']) > 3:
            st.markdown(f"<li>... and {len(coach['focus_areas']) - 3} more</li>", unsafe_allow_html=True)

        st.markdown("</ul></div>", unsafe_allow_html=True)


def check_api_health() -> bool:
    """Check if the API is healthy and accessible."""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False


def display_api_status():
    """Display API connection status."""
    if check_api_health():
        st.success("✅ API Connected")
    else:
        st.error("❌ API Disconnected")
        st.info("Start the server: `python src/main.py`")


def export_conversation():
    """Export conversation to JSON."""
    if st.session_state.messages:
        conversation_data = {
            "user_id": st.session_state.user_id,
            "coach_id": st.session_state.selected_coach,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "messages": st.session_state.messages
        }
        return json.dumps(conversation_data, indent=2)
    return None


def main():
    """Main Streamlit application."""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🎯 Career Coach Agents</h1>
        <p>Get personalized career guidance from AI specialists</p>
    </div>
    """, unsafe_allow_html=True)

    # API Status indicator
    with st.container():
        col1, col2, col3 = st.columns([2, 1, 1])
        with col3:
            display_api_status()

    # Sidebar
    with st.sidebar:
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("🔧 Configuration")

        # User ID
        st.text_input(
            "User ID",
            value=st.session_state.user_id,
            key="user_id_input",
            help="Unique identifier for your conversation session"
        )
        st.session_state.user_id = st.session_state.user_id_input

        # Load coaches
        if st.button("🔄 Refresh Coaches"):
            st.session_state.coaches_data = load_coaches()

        # Load coaches on first run
        if not st.session_state.coaches_data:
            with st.spinner("Loading coaches..."):
                st.session_state.coaches_data = load_coaches()

        st.markdown('</div>', unsafe_allow_html=True)

        # Coach selection
        if st.session_state.coaches_data:
            st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
            st.header("👥 Select Coach")

            coach_options = {
                f"{coach['name']} - {coach['specialty']}": coach['id']
                for coach in st.session_state.coaches_data
            }

            selected_coach_name = st.selectbox(
                "Choose your career coach:",
                options=list(coach_options.keys()),
                key="coach_selector"
            )

            if selected_coach_name:
                st.session_state.selected_coach = coach_options[selected_coach_name]

                # Display selected coach info
                selected_coach_data = next(
                    (coach for coach in st.session_state.coaches_data
                     if coach['id'] == st.session_state.selected_coach),
                    None
                )

                if selected_coach_data:
                    display_coach_card(selected_coach_data)

            st.markdown('</div>', unsafe_allow_html=True)

        # User context
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("📝 Context")
        user_context = st.text_area(
            "Tell us about your situation:",
            placeholder="e.g., Recent graduate, 5 years experience in marketing, looking for career change...",
            height=100,
            key="user_context"
        )
        st.markdown('</div>', unsafe_allow_html=True)

        # Controls
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.header("🎮 Controls")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ Clear Chat"):
                st.session_state.messages = []
                st.session_state.conversation_started = False
                st.rerun()

        with col2:
            if st.button("🔄 Reset Memory"):
                if reset_conversation(st.session_state.user_id):
                    st.success("Memory reset!")
                    st.session_state.messages = []
                    st.session_state.conversation_started = False
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("Failed to reset memory")

        # Export conversation
        if st.session_state.messages:
            conversation_json = export_conversation()
            if conversation_json:
                st.download_button(
                    label="📥 Export Chat",
                    data=conversation_json,
                    file_name=f"career_chat_{st.session_state.user_id}_{int(time.time())}.json",
                    mime="application/json"
                )

        st.markdown('</div>', unsafe_allow_html=True)

        # Statistics
        if st.session_state.messages:
            st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
            st.header("📊 Session Stats")

            user_messages = len([m for m in st.session_state.messages if m["role"] == "user"])
            coach_messages = len([m for m in st.session_state.messages if m["role"] == "assistant"])

            col1, col2 = st.columns(2)
            with col1:
                st.metric("Your Messages", user_messages)
            with col2:
                st.metric("Coach Responses", coach_messages)

            st.markdown('</div>', unsafe_allow_html=True)

    # Main chat interface
    if not st.session_state.coaches_data:
        st.error("⚠️ Could not load coaches. Please check if the API server is running.")
        st.info("Start the server with: `python src/main.py`")
        return

    if not st.session_state.selected_coach:
        st.info("👈 Please select a career coach from the sidebar to start chatting.")
        return

    # Chat container
    chat_container = st.container()

    with chat_container:
        # Display conversation history
        for message in st.session_state.messages:
            if message["role"] == "user":
                st.markdown(f"""
                <div class="user-message">
                    <strong>You:</strong> {message["content"]}
                </div>
                """, unsafe_allow_html=True)
            else:
                coach_name = message.get("coach_name", "Coach")
                st.markdown(f"""
                <div class="coach-message">
                    <strong>{coach_name}:</strong> {message["content"]}
                </div>
                """, unsafe_allow_html=True)

    # Chat input
    with st.container():
        st.markdown("---")

        # Get selected coach data for display
        selected_coach_data = next(
            (coach for coach in st.session_state.coaches_data
             if coach['id'] == st.session_state.selected_coach),
            None
        )

        coach_name = selected_coach_data['name'] if selected_coach_data else "Coach"

        # Chat input form
        with st.form(key="chat_form", clear_on_submit=True):
            col1, col2 = st.columns([4, 1])

            with col1:
                user_input = st.text_input(
                    f"💬 Chat with {coach_name}:",
                    placeholder="Type your message here...",
                    key="user_input"
                )

            with col2:
                send_button = st.form_submit_button("Send 📤")

        # Process user input
        if send_button and user_input.strip():
            # Add user message to chat
            st.session_state.messages.append({
                "role": "user",
                "content": user_input
            })

            # Show thinking indicator
            with st.spinner(f"{coach_name} is thinking..."):
                # Send to API
                response = send_message(
                    message=user_input,
                    coach_id=st.session_state.selected_coach,
                    user_id=st.session_state.user_id,
                    user_context=st.session_state.get("user_context", "")
                )

            if response:
                # Add coach response to chat
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": response,
                    "coach_name": coach_name
                })
                st.session_state.conversation_started = True
            else:
                st.error("Failed to get response from coach. Please try again.")

            # Rerun to update the chat display
            st.rerun()

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>🎯 Career Coach Agents - Powered by AI |
        <a href="http://localhost:8000/docs" target="_blank">API Docs</a> |
        User ID: {}</p>
    </div>
    """.format(st.session_state.user_id), unsafe_allow_html=True)


def home_page():
    """Display the home page with overview and navigation."""

    # Hero section
    st.markdown("""
    <div class="main-header">
        <h1>🎯 Career Coach Agents</h1>
        <p>Your AI-powered career guidance platform</p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Available Coaches", "4", help="Specialized AI career coaches")
    with col2:
        st.metric("Specialties", "4", help="Different areas of expertise")
    with col3:
        st.metric("Multi-User", "✅", help="Supports multiple users")
    with col4:
        st.metric("Real-time", "✅", help="Streaming conversations")

    st.markdown("---")

    # Features overview
    st.markdown("## 🌟 Features")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        ### 🎯 Specialized Coaches
        - **Career Assessment** - Dr. Sarah Chen
        - **Resume Builder** - Marcus Rodriguez
        - **LinkedIn Optimizer** - Emma Thompson
        - **Networking Strategy** - David Kim

        ### 💬 Smart Conversations
        - Real-time streaming responses
        - Context-aware conversations
        - Multi-user support
        - Conversation memory
        """)

    with col2:
        st.markdown("""
        ### 📊 Analytics & Insights
        - Usage tracking and analytics
        - Performance monitoring
        - Conversation export
        - Session management

        ### ⚙️ Customizable
        - Flexible API configuration
        - User preference settings
        - Data import/export
        - Theme customization
        """)

    st.markdown("---")

    # Quick start guide
    st.markdown("## 🚀 Quick Start")

    with st.expander("Getting Started Guide", expanded=True):
        st.markdown("""
        1. **🔧 Setup**: Ensure the API server is running (`python src/main.py`)
        2. **👥 Choose Coach**: Select a coach that matches your needs
        3. **💬 Start Chatting**: Begin your conversation with personalized guidance
        4. **📊 Track Progress**: Monitor your sessions in the Analytics page
        5. **⚙️ Customize**: Adjust settings to your preferences
        """)

    # Navigation buttons
    st.markdown("## 🧭 Navigation")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("💬 Start Chat", type="primary", use_container_width=True):
            st.switch_page("pages/1_🎯_Chat.py")

    with col2:
        if st.button("👥 Meet Coaches", use_container_width=True):
            st.switch_page("pages/2_👥_Coaches.py")

    with col3:
        if st.button("📊 View Analytics", use_container_width=True):
            st.switch_page("pages/3_📊_Analytics.py")

    with col4:
        if st.button("⚙️ Settings", use_container_width=True):
            st.switch_page("pages/4_⚙️_Settings.py")

    # API status
    st.markdown("---")
    st.markdown("## 🔌 System Status")

    col1, col2 = st.columns([1, 3])
    with col1:
        if check_api_health():
            st.success("✅ API Online")
        else:
            st.error("❌ API Offline")

    with col2:
        st.info("API Endpoint: http://localhost:8000")


if __name__ == "__main__":
    # Check if we're on the main page or a subpage
    if len(st.session_state.get("page_history", [])) == 0:
        home_page()
    else:
        main()
