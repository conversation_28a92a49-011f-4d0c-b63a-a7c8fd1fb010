import streamlit as st
import requests
import json

# Page configuration
st.set_page_config(
    page_title="Career Coaches",
    page_icon="👥",
    layout="wide"
)

# Configuration
API_BASE_URL = "http://localhost:8000"
CAREER_COACHES_API = f"{API_BASE_URL}/career-coaches"

# Custom CSS
st.markdown("""
<style>
    .coach-detail-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .focus-area-tag {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        margin: 0.2rem;
        display: inline-block;
        font-size: 0.9rem;
    }
    
    .specialty-badge {
        background: #ff6b6b;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: bold;
        display: inline-block;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)


def load_coaches():
    """Load coaches from API."""
    try:
        response = requests.get(f"{CAREER_COACHES_API}/coaches", timeout=5)
        if response.status_code == 200:
            return response.json().get("coaches", [])
        else:
            st.error(f"Failed to load coaches: {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        st.error(f"Could not connect to API: {e}")
        return []


def display_coach_detailed(coach):
    """Display detailed coach information."""
    st.markdown(f"""
    <div class="coach-detail-card">
        <h2>🎯 {coach['name']}</h2>
        <div class="specialty-badge">{coach['specialty']}</div>
        <h4>🎯 Areas of Expertise:</h4>
    </div>
    """, unsafe_allow_html=True)
    
    # Focus areas as tags
    focus_areas_html = ""
    for area in coach['focus_areas']:
        focus_areas_html += f'<span class="focus-area-tag">{area}</span>'
    
    st.markdown(focus_areas_html, unsafe_allow_html=True)
    
    # Sample questions
    st.subheader("💬 Sample Questions to Ask")
    
    sample_questions = get_sample_questions(coach['id'])
    for i, question in enumerate(sample_questions, 1):
        st.write(f"{i}. {question}")
    
    st.markdown("---")


def get_sample_questions(coach_id):
    """Get sample questions for each coach."""
    questions = {
        "career_assessment": [
            "I'm feeling lost in my career. Can you help me figure out what direction to take?",
            "How do I identify my strengths and what careers would be a good fit?",
            "I want to change careers but don't know where to start. What's your advice?",
            "What questions should I ask myself when evaluating career options?",
            "How do I balance passion with practical considerations in career planning?"
        ],
        "resume_builder": [
            "My resume isn't getting me interviews. What might be wrong?",
            "How do I optimize my resume for ATS systems?",
            "What's the best way to quantify my achievements on my resume?",
            "Should I use a different resume format for different industries?",
            "How do I write a compelling professional summary?"
        ],
        "linkedin_optimizer": [
            "How do I create a LinkedIn headline that stands out?",
            "What should I include in my LinkedIn summary?",
            "How often should I post content on LinkedIn?",
            "What's the best strategy for growing my LinkedIn network?",
            "How do I showcase my expertise through LinkedIn content?"
        ],
        "networking_strategy": [
            "I'm an introvert. How can I network effectively?",
            "What's the best way to approach someone for an informational interview?",
            "How do I maintain professional relationships over time?",
            "What should I do at networking events to make meaningful connections?",
            "How do I follow up after meeting someone at a networking event?"
        ]
    }
    return questions.get(coach_id, [])


def main():
    """Main coaches page."""
    st.title("👥 Meet Your Career Coaches")
    st.markdown("Get to know our specialized AI career coaches and their areas of expertise.")
    
    # Load coaches
    with st.spinner("Loading coaches..."):
        coaches = load_coaches()
    
    if not coaches:
        st.error("Could not load coaches. Please ensure the API server is running.")
        st.info("Start the server with: `python src/main.py`")
        return
    
    # Display coaches
    st.markdown(f"## 🌟 {len(coaches)} Specialized Coaches Available")
    
    for coach in coaches:
        display_coach_detailed(coach)
    
    # Quick comparison
    st.markdown("## 📊 Quick Comparison")
    
    comparison_data = []
    for coach in coaches:
        comparison_data.append({
            "Coach": coach['name'],
            "Specialty": coach['specialty'],
            "Focus Areas": len(coach['focus_areas']),
            "Best For": get_best_for(coach['id'])
        })
    
    st.table(comparison_data)
    
    # Call to action
    st.markdown("---")
    st.markdown("""
    ## 🚀 Ready to Start?
    
    Choose a coach that matches your needs and start your conversation in the **Chat** page.
    Each coach brings unique expertise and a personalized approach to help you achieve your career goals.
    """)
    
    col1, col2, col3 = st.columns(3)
    with col2:
        if st.button("💬 Start Chatting", type="primary", use_container_width=True):
            st.switch_page("pages/1_🎯_Chat.py")


def get_best_for(coach_id):
    """Get what each coach is best for."""
    best_for = {
        "career_assessment": "Career exploration & planning",
        "resume_builder": "Job applications & ATS optimization", 
        "linkedin_optimizer": "Personal branding & networking",
        "networking_strategy": "Building professional relationships"
    }
    return best_for.get(coach_id, "General career guidance")


if __name__ == "__main__":
    main()
